// publishCollectionMold.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';
import fetch from 'node-fetch';

// Parse command-line arguments (expecting --id=<moldId>)
const args = minimist(process.argv.slice(2));
const moldId = args.id;
if (!moldId) {
  console.error('ERROR: No mold id provided. Use --id=<moldId>');
  process.exit(1);
}
console.log(`INFO: Received mold id: ${moldId}`);

// Initialize Supabase client using your service key.
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase URL or key in environment variables.');
  process.exit(1);
}
console.log(`INFO: Initializing Supabase client...`);
const supabase = createClient(supabaseUrl, supabaseKey);

// Derive Shopify REST endpoint for smart collections.
// Replace "graphql.json" with "smart_collections.json".
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}
const smartCollectionsEndpoint = shopifyEndpoint.replace('graphql.json', 'smart_collections.json');
console.log(`INFO: Shopify smart collections endpoint set to: ${smartCollectionsEndpoint}`);

/**
 * Generates a handle for a mold collection.
 * The handle is built by concatenating the brand, mold, and type, converting to lowercase,
 * and then replacing certain characters per the specification.
 *
 * @param {string} brand - The brand name.
 * @param {string} mold - The mold name.
 * @param {string} type - The mold type.
 * @returns {string} - The generated handle.
 */
function generateMoldHandle(brand, mold, type) {
  console.log(`INFO: Generating handle for brand: "${brand}", mold: "${mold}", type: "${type}"`);
  let base = (brand + " " + mold + " " + type).toLowerCase();
  // Replace spaces with dashes.
  base = base.replace(/ /g, "-");
  // Remove apostrophes.
  base = base.replace(/'/g, "");
  // Replace periods with dashes.
  base = base.replace(/\./g, "-");
  // Replace vertical bars with dashes.
  base = base.replace(/\|/g, "-");
  // Remove forward slashes.
  base = base.replace(/\//g, "");
  // Replace ampersands with dashes.
  base = base.replace(/&/g, "-");
  // Remove parentheses.
  base = base.replace(/\(/g, "");
  base = base.replace(/\)/g, "");
  // Remove double quotes.
  base = base.replace(/"/g, "");
  // Replace any double dashes with a single dash.
  while (base.includes("--")) {
    base = base.replace(/--/g, "-");
  }
  console.log(`INFO: Generated collection handle: ${base}`);
  return base; // (Optionally append "-mold" if desired.)
}

/**
 * Returns a promise that resolves after the given number of milliseconds.
 * @param {number} ms - The delay in milliseconds.
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Enqueues check_if_mps_is_ready tasks for all unuploaded MPS records related to a mold.
 * @param {number} moldId - The mold ID
 * @param {string} moldName - The mold name for logging
 */
async function enqueueCheckMpsTasksForMold(moldId, moldName) {
  try {
    console.log(`INFO: Finding unuploaded MPS records for mold ${moldName} (id: ${moldId})...`);

    // Find all MPS records for this mold that haven't been uploaded to Shopify yet
    const { data: unuploadedMps, error: mpsError } = await supabase
      .from('t_mps')
      .select('id')
      .eq('mold_id', moldId)
      .is('shopify_collection_uploaded_at', null);

    if (mpsError) {
      console.error(`ERROR: Failed to fetch unuploaded MPS records for mold ${moldName}:`, mpsError);
      return;
    }

    if (!unuploadedMps || unuploadedMps.length === 0) {
      console.log(`INFO: No unuploaded MPS records found for mold ${moldName}.`);
      return;
    }

    console.log(`INFO: Found ${unuploadedMps.length} unuploaded MPS records for mold ${moldName}. Enqueueing check_if_mps_is_ready tasks...`);

    // Enqueue check_if_mps_is_ready task for each unuploaded MPS
    const tasksToEnqueue = unuploadedMps.map(mps => ({
      task_type: 'check_if_mps_is_ready',
      payload: { id: mps.id },
      status: 'pending',
      scheduled_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes from now
      created_at: new Date().toISOString(),
      enqueued_by: `publish_mold_collection_${moldId}`
    }));

    const { error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert(tasksToEnqueue);

    if (enqueueError) {
      console.error(`ERROR: Failed to enqueue check_if_mps_is_ready tasks for mold ${moldName}:`, enqueueError);
    } else {
      console.log(`INFO: Successfully enqueued ${tasksToEnqueue.length} check_if_mps_is_ready tasks for mold ${moldName}.`);
    }

  } catch (error) {
    console.error(`ERROR: Unexpected error while enqueueing MPS tasks for mold ${moldName}:`, error);
  }
}

async function main() {
  console.log('INFO: Starting publishCollectionMold process...');
  console.log(`INFO: Publishing mold collection for mold id ${moldId}...`);
  
  // Retrieve the mold record from t_molds.
  console.log(`INFO: Fetching mold record with id ${moldId} from t_molds...`);
  let { data: moldRecord, error: moldError } = await supabase
    .from('t_molds')
    .select('*')
    .eq('id', moldId)
    .single();
  
  if (moldError) {
    console.error('ERROR: Error fetching mold from t_molds:', moldError);
    process.exit(1);
  }
  console.log(`INFO: Mold record retrieved: ${JSON.stringify(moldRecord, null, 2)}`);
  
  // Retrieve the associated brand from t_brands.
  console.log(`INFO: Fetching associated brand (id: ${moldRecord.brand_id}) from t_brands...`);
  const { data: brandData, error: brandError } = await supabase
    .from('t_brands')
    .select('brand, handle')
    .eq('id', moldRecord.brand_id)
    .single();
  
  if (brandError) {
    console.error('ERROR: Error fetching brand from t_brands:', brandError);
    process.exit(1);
  }
  console.log(`INFO: Brand record retrieved: ${JSON.stringify(brandData, null, 2)}`);
  moldRecord.brand = brandData.brand;
  // We'll use the brand's handle for the "Back to" link.
  const brandHandle = brandData.handle;
  console.log(`INFO: Using brand handle: ${brandHandle}`);
  
 // Construct the image URL for the mold using configuration values.
console.log(`INFO: Constructing image URL for mold id ${moldId} using new config...`);

const { data: publicServerData, error: publicServerError } = await supabase
  .from('t_config')
  .select('value')
  .eq('key', 'public_image_server')
  .single();

const { data: folderData, error: folderError } = await supabase
  .from('t_config')
  .select('value')
  .eq('key', 'folder_molds')
  .single();

let imageUrlForCollection = "";
if (publicServerError || !publicServerData) {
  console.error('ERROR: Unable to fetch public_image_server config:', publicServerError);
} else if (folderError || !folderData) {
  console.error('ERROR: Unable to fetch folder_molds config:', folderError);
} else {
  imageUrlForCollection = `${publicServerData.value}/${folderData.value}/${moldId}.jpg`;
  console.log(`INFO: Constructed image URL for collection: ${imageUrlForCollection}`);
}



  
  // Skip eligibility check - trust that this task was properly enqueued by check_if_mold_is_ready
  console.log('INFO: Mold is eligible for publishing.');
  
  // Generate the collection handle.
  const collectionHandle = generateMoldHandle(moldRecord.brand, moldRecord.mold, moldRecord.type);
  
  // Build the Body HTML.
  console.log('INFO: Building Body HTML for Shopify smart collection...');
  const bodyHtml = `<p><strong>Mold: The ${moldRecord.brand} ${moldRecord.mold} ${moldRecord.type}  --  Speed: ${moldRecord.speed} | Glide: ${moldRecord.glide} | Turn: ${moldRecord.turn} | Fade: ${moldRecord.fade}</strong><br>${moldRecord.description}</p><p><a href='/collections/${brandHandle}'>Back to all ${moldRecord.brand} Molds</a></p>`;
  
  // Build the Title.
  const title = `${moldRecord.brand} ${moldRecord.mold} ${moldRecord.type}`;
  console.log(`INFO: Title for collection: ${title}`);
  
  // Build the payload for Shopify smart collection.
  console.log('INFO: Constructing payload for Shopify smart collection...');
  const payload = {
    smart_collection: {
      title: title,
      body_html: bodyHtml,
      handle: collectionHandle,
      sort_order: "created-desc",        // Valid sort order
      template_suffix: "mold-collection",
      published: true,
      published_scope: "global",         // 🔑 NEW: Ensures visibility across all sales channels
      // disjunctive:false => all conditions must match (logical AND).
      disjunctive: false,
      image: {
        src: imageUrlForCollection
      },
      rules: [
        {
          column: "tag",
          relation: "equals",
          condition: "disc_mold_" + moldRecord.mold
        },
        {
          column: "variant_inventory",
          relation: "greater_than",
          condition: "0"
        }
      ]
    }
  };
  console.log('INFO: Payload constructed:');
  console.log(JSON.stringify(payload, null, 2));
  
  // Attempt to create the smart collection on Shopify.
  try {
    console.log("INFO: Sending payload to Shopify smart collections endpoint...");
    const response = await fetch(smartCollectionsEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });
    
    console.log("INFO: Response received from Shopify. Processing response...");
    const result = await response.json();
    console.log("INFO: Shopify response JSON:");
    console.log(JSON.stringify(result, null, 2));
    
    if (!response.ok) {
      throw new Error(`Error creating smart collection for mold "${moldRecord.mold}": ${JSON.stringify(result)}`);
    }
    console.log(`INFO: Successfully created collection for mold ${moldRecord.mold}:`);
    console.log(JSON.stringify(result.smart_collection, null, 2));
    
    // Update t_molds: set shopify_collection_created_at to now and update the success note.
    const successNote = "Success! Mold Collection created on Shopify through webhook > .js > Shopify API.";
    console.log("INFO: Updating t_molds record with success note and current timestamp...");
    const { error: updateError } = await supabase
      .from('t_molds')
      .update({
        shopify_collection_created_at: new Date().toISOString(),
        shopify_collection_uploaded_notes: successNote
      })
      .eq('id', moldId);
    if (updateError) {
      console.error(`ERROR: Failed to update shopify_collection_created_at for mold ${moldRecord.mold}:`, updateError);
      process.exit(1);
    }
    console.log(`INFO: Successfully updated shopify_collection_created_at for mold ${moldRecord.mold}.`);

    // Enqueue check_if_mps_is_ready tasks for related unuploaded MPS records
    await enqueueCheckMpsTasksForMold(moldId, moldRecord.mold);

  } catch (err) {
    console.error(`ERROR: Failed to create collection for mold ${moldRecord.mold}: ${err.message}`);
    // On error, update t_molds.shopify_collection_uploaded_notes with the error message.
    const { error: noteError } = await supabase
      .from('t_molds')
      .update({ shopify_collection_uploaded_notes: err.message })
      .eq('id', moldId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_collection_uploaded_notes for mold ${moldRecord.mold}: ${noteError.message}`);
    }

    // Even on failure, enqueue check_if_mps_is_ready tasks for related unuploaded MPS records
    await enqueueCheckMpsTasksForMold(moldId, moldRecord.mold);

    process.exit(1);
  }
  
  console.log("INFO: publishCollectionMold process completed successfully.");
}

main().catch(err => {
  console.error('ERROR: Unexpected error:', err);
  process.exit(1);
});
